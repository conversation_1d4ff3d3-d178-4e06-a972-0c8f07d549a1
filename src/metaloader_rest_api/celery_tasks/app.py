from datetime import datetime
from typing import Optional, Sequence
from uuid import UUID

import structlog
from celery import signals

from metaloader_rest_api import schemas
from metaloader_rest_api.ceh_service import ceh_service_api
from metaloader_rest_api.celery_tasks import logic
from metaloader_rest_api.celery_tasks.database_connectivity import (
    AbstractBoundSessionTask,
    create_bound_session_task_class,
    create_sqlalchemy_engine,
)
from metaloader_rest_api.celery_tasks.settings import Settings
from metaloader_rest_api.data_model import data_model_api
from metaloader_rest_api.flow_processor import flow_processor_api
from metaloader_rest_api.helpers import read_settings_from_env

from .app_factory import get_celery_app
from .logging import get_logger, setup_logging
from .logic import task_manager

logger = get_logger(__name__)


settings = read_settings_from_env(Settings)


@signals.setup_logging.connect
def on_setup_logging(**kwargs):  # noqa
    # Методика настройки страктлога в селери взята отсюда:
    # https://github.com/hynek/structlog/issues/287
    setup_logging(log_format=settings.log_format, log_level=settings.log_level)


@signals.task_prerun.connect
def on_task_prerun(sender, task_id, task, args, kwargs, **_):  # noqa
    structlog.contextvars.bind_contextvars(task_id=task_id, task_name=task.name)


@signals.task_postrun.connect
def on_task_postrun(sender, task_id, task, args, kwargs, **_):  # noqa
    structlog.contextvars.clear_contextvars()


app = get_celery_app(settings)
BoundSessionTask = create_bound_session_task_class(
    engine=create_sqlalchemy_engine(database_url=settings.database_url.unicode_string())
)


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="run_release_sources_integration_etl",
)
def run_release_sources_integration_etl(
    self: AbstractBoundSessionTask,
    tx_uid: str,
    # Селери по умолчанию использует JSON-сериализацию аргументов тасок (pickle небезопасен), а
    # встроенный модуль json не умеет сериализовывать `enum.Enum`. Поэтому на стороне селери-клиента
    # нужно убедиться, что значения этого параметра всегда соответствуют `schemas.TransactionMode`.
    integration_mode: str,
    # NOTE: "резиновая" сигнатура селери-таски предохраняет воркер от падений, когда при обновлениях
    # на нём ещё работает старая версия кода, а клиент (вебап) уже публикует таски на новой
    **kwargs,  # noqa
) -> None:
    logic.run_release_sources_integration_etl(
        session=self.session,
        tx_uid=UUID(tx_uid),
        integration_mode=schemas.TransactionMode(integration_mode),
        do_commit=True,
    )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="dry_run_release_sources_integration_etl",
)
def dry_run_release_sources_integration_etl(
    self: AbstractBoundSessionTask,
    tx_uid: str,
    integration_mode: str,
    **kwargs,  # noqa
) -> None:
    logic.run_release_sources_integration_etl(
        session=self.session,
        tx_uid=UUID(tx_uid),
        integration_mode=schemas.TransactionMode(integration_mode),
        do_commit=False,
    )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="run_resources_integration_etl",
)
def run_resources_integration_etl(
    self: AbstractBoundSessionTask,
    tx_uid: str,
    **kwargs,  # noqa
) -> None:
    logic.run_resources_integration_etl(
        session=self.session,
        tx_uid=UUID(tx_uid),
        retry_task=lambda exc: self.retry(exc=exc),
        retries_limit_reached=self.retries_limit_reached,
    )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_flows_from_airflow",
)
def load_flows_from_airflow(
    self: AbstractBoundSessionTask,
    load_id: str,
    module: str,
    version: str,
    effective_date: datetime,
    af_url: str,
    page_size: int,
    keep_dag: bool,
    with_tasks: bool,
    parse_master_flows: bool,
    limit: int,
    webhook: Optional[str],
    **kwargs,  # noqa
) -> None:
    logic.load_flows_from_airflow(
        session=self.session,
        load_id=UUID(load_id),
        module=module,
        version=version,
        effective_date=effective_date,
        af_url=af_url,
        af_username=settings.airflow_username,
        af_password=settings.airflow_password,
        page_size=page_size,
        keep_dag=keep_dag,
        with_tasks=with_tasks,
        parse_master_flows=parse_master_flows,
        limit=limit,
        webhook=webhook,
    )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_resources_from_providers",
)
def load_resources_from_providers(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    module: str,
    version: str,
    effective_date: datetime,
    uni_provider_base_url: str,
    ceh_provider_base_url: str,
    webhook: Optional[str],
    **kwargs,  # noqa
) -> None:
    logic.load_resources_from_providers(
        session=self.session,
        load_id=load_id,
        module=module,
        version=version,
        effective_date=effective_date,
        uni_provider_base_url=uni_provider_base_url,
        ceh_provider_base_url=ceh_provider_base_url,
        webhook=webhook,
    )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="process_flows",
)
def process_flows(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: flow_processor_api.ProcessFlowsParams,
    **kwargs,
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("process_flows", params=params)
    with task_manager(load_id, self.session, log):
        flow_processor_api.process_flows(
            self.session,
            load_id,
            effective_date,
            params,
            log,
        )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="check_transactions_statuses",
)
def check_transactions_statuses(
    self: AbstractBoundSessionTask,
    load_id: str,
    transactions_ids: Sequence[str],
    **kwargs,  # noqa
) -> None:
    logic.check_transactions_statuses(
        session=self.session,
        load_id=UUID(load_id),
        transactions_ids=tuple(UUID(uid) for uid in transactions_ids),
    )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_data_model",
)
def load_data_model(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: data_model_api.LoadDataModelParams,
    **kwargs,
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("load_data_model", params=params)
    with task_manager(load_id, self.session, log):
        data_model_api.load_data_model(
            self.session,
            load_id,
            effective_date,
            params,
            log,
        )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_logical_data_model",
)
def load_logical_data_model(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: data_model_api.LoadLogicalDataModelParams,
    **kwargs,
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("load_logical_data_model", params=params)
    with task_manager(load_id, self.session, log):
        data_model_api.load_logical_data_model(
            self.session,
            load_id,
            effective_date,
            params,
            log,
        )


@app.task(
    bind=True,
    base=BoundSessionTask,
    name="load_services",
)
def load_services(
    self: AbstractBoundSessionTask,
    load_id: UUID,
    effective_date: datetime,
    params: ceh_service_api.LoadServicesParams,
    **kwargs,
) -> None:
    log = logger.bind(load_id=load_id, effective_date=effective_date)
    log.info("load_services", params=params)
    with task_manager(load_id, self.session, log):
        ceh_service_api.load_services(
            session=self.session,
            load_id=load_id,
            effective_date=effective_date,
            params=params,
            log=log,
        )
