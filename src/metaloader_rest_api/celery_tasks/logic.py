from contextlib import contextmanager, suppress
from datetime import datetime
from typing import Callable, Map<PERSON>, Optional, Tuple
from uuid import UUID

import httpx
from celery.exceptions import Retry
from sqlalchemy import text
from sqlalchemy.exc import NoResultFound, OperationalError, SQLAlchemyError
from sqlalchemy.orm import Session
from structlog import Bound<PERSON>ogger

from metaloader_rest_api import models, schemas
from metaloader_rest_api.af_defaults import AfDefaults
from metaloader_rest_api.celery_tasks.logging import get_logger
from metaloader_rest_api.flow_af_loader import af_load_flows
from metaloader_rest_api.logic import (
    close_tx,
    process_tx,
    update_transaction_instance_on_resolution,
)
from metaloader_rest_api.master_flow_repository import load_master_flows
from metaloader_rest_api.models import TransactionStatus
from metaloader_rest_api.module_repository import ModuleRepository
from metaloader_rest_api.resource_providers_resource_loader import \
    load_resources_from_resource_providers
from metaloader_rest_api.resource_repository import (
    ResourceRepository,
    ResourceStageSequentialRepository,
)
from metaloader_rest_api.schemas import ReleaseMetadata
from metaloader_rest_api.version_repository import VersionRepository

logger = get_logger(__name__)


def run_release_sources_integration_etl(
    session: Session,
    tx_uid: UUID,
    integration_mode: schemas.TransactionMode,
    do_commit: bool,
) -> None:
    transaction = (
        session.query(models.MasterFlowTransaction)
        .filter(models.MasterFlowTransaction.id == tx_uid)
        .one()
    )
    # noinspection PyTypeChecker
    if not _ensure_transaction_is_opened(transaction):
        return

    try:
        transaction.status = models.TransactionStatus.IN_PROGRESS.value
        session.commit()
    except Exception:  # noqa
        logger.exception("DB connection lost, transaction %s left intact", str(tx_uid))
        # Если повезло потерять соединение до того, как поменяли статус, то ничего не делаем,
        # даём возможность пользователю апи ещё раз позвать /commit_async для этой же транзакции
        return

    try:
        master_flow_errors = load_master_flows(
            session=session, tx_id=tx_uid, commit=do_commit
        )
    except Exception:  # noqa
        logger.exception("Uncaught ETL exception")
        # TO-DO: формат информирования об "общей" (не относящейся к конкретному мастер-потоку)
        # ошибке скопипащен из функции `load_master_flows`. По-хорошему, чтобы формат был точно
        # единообразен, стоит вынести эту логику в отдельную функцию.
        master_flow_errors = {"error": ["Unknown ETL error"]}

    if master_flow_errors or not do_commit:
        session.rollback()
    transaction_status = (
        schemas.TransactionStatus.ETL_ERROR
        if master_flow_errors
        else schemas.TransactionStatus.COMMITTED
        if do_commit
        else schemas.TransactionStatus.ROLLEDBACK
    )
    update_transaction_instance_on_resolution(
        transaction=transaction,  # type: ignore
        status=transaction_status,
        master_flow_errors=master_flow_errors,
    )
    session.add(transaction)
    # FIXME: если воркер упадёт в этот момент (на этой строке), или соединение с БД пропадёт,
    #  до того, как закоммитится смена статуса транзакции, то транзакция зависнет в in_progress
    #  до таймаута ==> пользователь апи создаст новую транзакцию с теми же действиями над
    #  мастер-потоками, а это риск порчи состояния метамодели.
    #  По-хорошему, нужно менять статус у объекта "Transaction" в той же БД-транзакции, в которой
    #  выполняются SQL-команды самого ETLя.
    session.commit()


def run_resources_integration_etl(
    session: Session,
    tx_uid: UUID,
    retry_task: Callable[[Optional[Exception]], Retry],
    retries_limit_reached: bool = False,
) -> None:
    # TODO: вынести получение транзакции и try-except в отдельную функцию
    try:
        # noinspection PyTypeChecker
        transaction: models.ResourceTransaction = (
            session.query(models.ResourceTransaction)
            .filter(models.ResourceTransaction.id == tx_uid)
            .one()
        )
    except NoResultFound:
        logger.warning("Transaction not found", tx_uid=tx_uid)
        return

    # noinspection PyTypeChecker
    if not _ensure_transaction_is_opened(transaction):
        return

    try:
        transaction.status = models.TransactionStatus.IN_PROGRESS.value
        session.commit()
    except Exception:  # noqa
        logger.exception("DB connection lost, transaction %s left intact", str(tx_uid))
        # Если повезло потерять соединение до того, как поменяли статус, то ничего не делаем,
        # даём возможность пользователю апи ещё раз позвать /commit для этой же транзакции
        return

    module_repository = ModuleRepository(session)
    version_repository = VersionRepository(session)
    resource_repository = ResourceRepository(session)
    # noinspection PyArgumentList
    release = ReleaseMetadata(**transaction.release_metadata)
    version_numbers = version_repository.parse(release.release_num)

    try:
        module_id = module_repository.get_id(release.release_module)
        version_id = version_repository.put(
            module_id=module_id,
            major=version_numbers[0],
            minor=version_numbers[1],
            fix=version_numbers[2],
            description=release.release_desc,
            build_date=release.release_date,
            delivery_date=transaction.created_at,
        )
        resource_repository.load(
            version_id=version_id,
            effective_date=transaction.created_at,
            stage_table=ResourceStageSequentialRepository.get_staging_table_name(
                load_id=tx_uid,
            ),
        )
        transaction.status = models.TransactionStatus.COMMITTED.value
        session.commit()
    except OperationalError as err:
        # Класс ошибок, которые обычно можно заретраить. Если будем ловить что-то ненужное, нужно
        # будет специфицировать более конкретные ошибки по тексту эксепшена.
        # Точно нужно ретраить ошибки сериализации транзакции и потерю коннекта к БД.
        # https://docs.sqlalchemy.org/en/20/errors.html#operationalerror
        session.rollback()
        logger.warning("Database error during ETL", tx_uid=tx_uid, error=str(err))
        if not retries_limit_reached:
            raise retry_task(err)
        with suppress(SQLAlchemyError):
            transaction.status = models.TransactionStatus.ROLLEDBACK.value
            session.commit()
            return
    except Exception:
        session.rollback()
        logger.exception("ETL failed with unexpected error")
        with suppress(Exception):
            transaction.status = models.TransactionStatus.ROLLEDBACK.value
            session.commit()
        raise


def load_flows_from_airflow(
    session: Session,
    load_id: UUID,
    module: str,
    version: str,
    effective_date: datetime,
    af_url: str,
    af_username: str,
    af_password: str,
    page_size: int = AfDefaults.DAG_PAGE_SIZE.value,
    keep_dag: bool = True,
    with_tasks: bool = True,
    parse_master_flows: bool = True,
    limit: int = AfDefaults.DAG_LIMIT.value,
    webhook: Optional[str] = None,
) -> None:
    logger.info("begin")
    rollback = False
    message = None
    try:
        process_tx(session, logger, load_id)
        result = af_load_flows(
            session=session,
            load_id=load_id,
            module=module,
            version=version,
            effective_date=effective_date,
            af_url=af_url,
            af_username=af_username,
            af_password=af_password,
            page_size=page_size,
            keep_dag=keep_dag,
            with_tasks=with_tasks,
            parse_master_flows=parse_master_flows,
            limit=limit,
        )
        message = str(result) if result else "OK"
    except Exception as exception:
        logger.exception("fail")
        rollback = True
        message = str(exception)
    finally:
        close_tx_exception = None
        try:
            close_tx(session, logger, load_id, rollback, message)
        except Exception as exception:
            logger.exception("fail", stage="close")
            close_tx_exception = exception
        finally:
            invoke_webhook(
                url=webhook,
                load_id=load_id,
                rollback=rollback,
                message=message,
                exception=close_tx_exception,
                logger=logger,
            )
            logger.info("end")


def load_resources_from_providers(
    session: Session,
    load_id: UUID,
    module: str,
    version: str,
    effective_date: datetime,
    uni_provider_base_url: str,
    ceh_provider_base_url: str,
    webhook: Optional[str] = None,
) -> None:
    logger.info("begin")
    rollback = False
    message = "OK"
    try:
        process_tx(session, logger, load_id)
        load_resources_from_resource_providers(
            session=session,
            load_id=load_id,
            module=module,
            version=version,
            effective_date=effective_date,
            uni_provider_base_url=uni_provider_base_url,
            ceh_provider_base_url=ceh_provider_base_url,
        )
    except Exception as exception:
        logger.exception("fail")
        rollback = True
        message = str(exception)
    finally:
        close_tx_exception = None
        try:
            close_tx(session, logger, load_id, rollback, message)
        except Exception as exception:
            logger.exception("fail", stage="close")
            close_tx_exception = exception
        finally:
            invoke_webhook(
                url=webhook,
                load_id=load_id,
                rollback=rollback,
                message=message,
                exception=close_tx_exception,
                logger=logger,
            )
            logger.info("end")


def check_transactions_statuses(
    session: Session,
    load_id: UUID,
    transactions_ids: Tuple[UUID, ...],
) -> None:
    rollback = False
    message = None
    try:
        transactions_statuses = get_non_commited_transactions_statuses(
            db_session=session,
            transactions_ids=transactions_ids,
        )
        if transactions_statuses:
            rollback = True
            transactions_ids = [str(uid) for uid in transactions_ids]
            transactions_statuses = {
                str(uid): status for uid, status in transactions_statuses.items()
            }
            message = (
                f"Non-commited transactions {transactions_statuses} "
                f"out of {transactions_ids}"
            )
    except Exception as exception:
        logger.exception("fail")
        rollback = True
        message = str(exception)
    finally:
        try:
            close_tx(session, logger, load_id, rollback, message)
        except Exception:
            logger.exception("fail", stage="close")
        finally:
            logger.info("end")


def invoke_webhook(
    url: Optional[str],
    load_id: UUID,
    rollback: bool,
    message: str,
    exception: Optional[Exception],
    logger: BoundLogger,
) -> None:
    if url is None:
        return

    log = logger.bind(
        action="invoke_webhook",
        url=url,
    )
    try:
        status = "succeeded" if rollback and exception is None else "failed"
        log.info(
            "begin",
            message=message,
            exception=exception,
            status=status,
        )
        response = httpx.post(
            url=url,
            json={
                "tx_id": str(load_id),
                "status": status,
                "message": message,
                "exception": str(exception) if exception else None,
            },
        )
        log.info("end", response=response)
    except Exception:
        log.exception("fail")


def get_non_commited_transactions_statuses(
    db_session: Session,
    transactions_ids: Tuple[UUID, ...],
) -> Mapping[UUID, str]:
    rows = db_session.execute(
        statement=text("""
              SELECT t.service_transaction_uid
                   , s.service_transaction_status_cd
                FROM metamodel.service_transaction_status t
                JOIN dict.dict_service_transaction_status s
                  ON s.service_transaction_status_rk = t.service_transaction_status_rk 
               WHERE t.service_transaction_uid       IN :service_transaction_uids
                 AND t.service_transaction_status_rk != :service_transaction_status_rk
            ORDER BY t.request_dttm
        """),
        params={
            "service_transaction_uids": transactions_ids,
            "service_transaction_status_rk": TransactionStatus.COMMITTED,
        },
    ).fetchall()

    return {row[0]: row[1] for row in rows}


@contextmanager
def task_manager(
    load_id: UUID,
    db_session: Session,
    log: BoundLogger,
):
    rollback = False
    message = None
    log.exception("begin")
    try:
        process_tx(db_session, log, load_id)
        yield None
    except Exception as exception:
        log.exception("fail")
        rollback = True
        message = str(exception)
    finally:
        close_tx(db_session, logger, load_id, rollback, message)
        log.info("end")


def _ensure_transaction_is_opened(transaction: models.BaseTransaction) -> bool:
    """
    NOTE: подразумевается, что селери-задача, зовущая эту функцию исполняется на воркере,
    который выполняет задачи строго одну за одной. Соответственно, если транзакция не
    "opened", то либо другой инстанс этой задачи её уже зарезолвил, либо воркер упал,
    не успев обновить её статус и она зависла в in_progress до таймаута  ¯\_(ツ)_/¯.
    При этом ETL для неё мог как выполнится, так и упасть с ошибкой (но их мы всегда ловим).
    """
    if transaction.status == models.TransactionStatus.OPENED.value:
        return True

    try:
        tx_status = models.TransactionStatus(transaction.status).name.lower()
    except ValueError:
        tx_status = str(transaction.status)

    logger.warning(
        "Attempting to work on transaction that has already been started processing",
        tx_status=tx_status,
    )
    return False
