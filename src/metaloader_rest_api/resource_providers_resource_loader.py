import itertools
from datetime import datetime
from uuid import UUID

import structlog
from sqlalchemy.orm import Session
from typing_extensions import Self

from metaloader_rest_api.common_repository import SessionResource
from metaloader_rest_api.loggable import Loggable
from metaloader_rest_api.models import ResourceAction
from metaloader_rest_api.module_repository import ModuleRepository
from metaloader_rest_api.resource_provider_client import ResourceProviderClient
from metaloader_rest_api.resource_repository import (
    ResourceRepository,
    ResourceStageBatchRepository,
)
from metaloader_rest_api.resource_repository._repository import StageResource
from metaloader_rest_api.version_repository import VersionRepository

logger = structlog.stdlib.get_logger(__name__)


class ResourceProvidersResourceLoader(Loggable):
    def __init__(
        self,
        uni_client: ResourceProviderClient,
        ceh_client: ResourceProviderClient,
        version_repository: VersionRepository,
        module_repository: ModuleRepository,
        session_resource: SessionResource,
        resource_stage_repository: ResourceStageBatchRepository,
        resource_repository: ResourceRepository,
    ):
        self._log = logger.bind(action="load_from_resource_providers")

        self._uni_client = uni_client
        self._ceh_client = ceh_client
        self._version_repository = version_repository
        self._module_repository = module_repository
        self._session_resource = session_resource
        self._resource_stage_repository = resource_stage_repository
        self._resource_repository = resource_repository

    def with_context(self, **kwarg) -> Self:
        for loggable in [
            self,
            self._uni_client,
            self._ceh_client,
            self._version_repository,
            self._module_repository,
            self._session_resource,
            self._resource_stage_repository,
            self._resource_repository,
        ]:
            loggable.with_context(**kwarg)
        return self

    def load(
        self,
        module: str,
        version: str,
        effective_date: datetime,
    ) -> None:
        self._log.info("begin")

        module_id = self._module_repository.get_id(module)
        self._log.info("get_module_id", module_id=module_id)
        version_parts = VersionRepository.parse(version)
        version_id = self._version_repository.put(
            module_id=module_id,
            major=version_parts[0],
            minor=version_parts[1],
            fix=version_parts[2],
            delivery_date=effective_date,
        )
        self._log.info("get_version_id", version_id=version_id)

        # NOTE: БД-транзакция будет держаться открытой всё время с момента загрузки первого батча
        #  строк внутри resource_stage_repository и до конца загрузки всех ресурсов. Это значит, что
        #  мы надолго займём соединение в постгресе -- здесь потенциальная проблема с БД в будущем.
        with (
            self._session_resource("load_resources_stage"),
            self._resource_stage_repository as resource_stage_repository,
            self._uni_client,
            self._ceh_client,
        ):
            all_resources_iter = itertools.chain(
                self._ceh_client.get_all_resources(),
                self._uni_client.get_all_resources(),
            )
            for resource_cd, definition in all_resources_iter:
                stage_record = StageResource(
                    resource_cd=resource_cd,
                    action=ResourceAction.ADD.value,
                    definition=definition,
                )
                resource_stage_repository.load_one(stage_record)

        with self._session_resource("load_resources"):
            self._resource_repository.load(
                version_id=version_id,
                effective_date=effective_date,
                stage_table=resource_stage_repository.table,
            )

        self._log.info("end", version_id=version_id)


def load_resources_from_resource_providers(
    session: Session,
    load_id: UUID,
    module: str,
    version: str,
    effective_date: datetime,
    uni_provider_base_url: str,
    ceh_provider_base_url: str,
) -> None:
    ResourceProvidersResourceLoader(
        uni_client=ResourceProviderClient(uni_provider_base_url),
        ceh_client=ResourceProviderClient(ceh_provider_base_url),
        version_repository=VersionRepository(session),
        module_repository=ModuleRepository(session),
        session_resource=SessionResource(session),
        resource_stage_repository=ResourceStageBatchRepository(
            session=session,
            table_id=load_id.hex,
        ),
        resource_repository=ResourceRepository(session),
    ).with_context(
        module=module,
        version=version,
        effective_date=str(effective_date),
    ).load(
        module=module,
        version=version,
        effective_date=effective_date,
    )
